/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.page-container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.main-layout {
    display: flex;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    padding: 40px;
    max-width: 70%;
}

/* Hero Section */
.hero-section {
    display: flex;
    align-items: flex-start;
    margin-bottom: 40px;
    gap: 30px;
}

.hero-illustration {
    flex-shrink: 0;
    margin: 0;
}

.hero-illustration img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e0e0e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.hero-content {
    flex: 1;
}

.company-name {
    font-size: 4rem;
    font-weight: 300;
    color: #2c5f7a;
    margin-bottom: 20px;
    letter-spacing: -1px;
}

.tagline {
    font-size: 1.5rem;
    font-weight: 500;
    color: #00bfa5;
    margin-bottom: 20px;
    line-height: 1.4;
}

.highlight-customers {
    color: #2c5f7a;
    font-weight: 600;
}

.company-description {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c5f7a;
    margin-bottom: 15px;
}

.detailed-description {
    font-size: 1rem;
    color: #666;
    line-height: 1.6;
}

/* Company Info */
.company-info {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;
}

.info-item {
    font-size: 0.9rem;
    color: #666;
}

.info-label {
    color: #00bfa5;
    font-weight: 600;
    font-size: 0.8rem;
}

/* Problems & Answers Section */
.problems-answers {
    margin-bottom: 50px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 2px dotted #e0e0e0;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c5f7a;
}

.qa-item {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.problem, .answer {
    flex: 1;
    font-size: 1rem;
    color: #666;
}

.arrow {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.arrow-circle {
    width: 40px;
    height: 40px;
    background-color: #00bfa5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.qa-separator {
    height: 1px;
    background: linear-gradient(to right, transparent, #e0e0e0, transparent);
    margin: 20px 0;
}

/* Key Staff Section */
.key-staff {
    margin-bottom: 50px;
}

.key-staff .section-title {
    color: #00bfa5;
    margin-bottom: 30px;
}

.staff-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.staff-card {
    background-color: #f8f8f8;
    padding: 20px;
    border-radius: 8px;
}

.staff-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 5px;
}

.staff-title {
    color: #666;
    margin-bottom: 8px;
}

.staff-tenure {
    color: #00bfa5;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Contact Section */
.contact-section {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e0e0e0;
}

.contact-title {
    color: #00bfa5;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 1rem;
}

.contact-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.contact-details {
    display: flex;
    gap: 40px;
}

.contact-person, .contact-company {
    font-size: 1rem;
}

.contact-name, .company-name {
    font-weight: 600;
    color: #020202;
    margin-bottom: 1px;
}

.contact-email, .contact-phone, .company-address {
    color: #020202;
    margin-bottom: 3px;
}

.contact-email {
    color: #020202;
    text-decoration: none;
}

.company-logo img {
    max-width: 120px;
    height: auto;
    opacity: 0.7;
}

/* Timeline Sidebar */
.timeline-sidebar {
    width: 30%;
    background-color: #fffbe9;
    padding: 30px 20px;
    border-left: 1px solid #e0e0e0;
}

.timeline-header {
    margin-bottom: 30px;
}

.timeline-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c5f7a;
    margin-bottom: 10px;
}

.timeline-subtitle {
    font-size: 0.8rem;
    font-weight: 600;
    color: #00bfa5;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timeline-container {
    position: relative;
    padding-left: 20px;
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #00bfa5, #2c5f7a);
    border-radius: 1px;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding-left: 25px;
}

.timeline-dot {
    position: absolute;
    left: -15px;
    top: 5px;
    width: 12px;
    height: 12px;
    background-color: #00bfa5;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #00bfa5;
}

.timeline-year {
    font-weight: 600;
    color: #2c5f7a;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.timeline-status {
    font-style: italic;
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.timeline-status.active {
    color: #00bfa5;
}

.timeline-status.inactive {
    color: #999;
}

.timeline-project {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.timeline-description {
    font-size: 0.85rem;
    color: #666;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-layout {
        flex-direction: column;
    }
    
    .main-content {
        max-width: 100%;
        padding: 20px;
    }
    
    .timeline-sidebar {
        width: 100%;
    }
    
    .hero-section {
        flex-direction: column;
        text-align: center;
    }

    .hero-illustration img {
        width: 150px;
        height: 150px;
        margin-bottom: 20px;
    }

    .company-name {
        font-size: 2.5rem;
    }
    
    .company-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .qa-item {
        flex-direction: column;
        text-align: center;
    }
    
    .staff-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-details {
        flex-direction: column;
        gap: 20px;
    }
}

/* Semantic HTML Element Styling Overrides */
/* Ensure h4 elements in staff cards maintain styling */
.staff-card h4.staff-name {
    font-weight: 500;
    font-size: 1rem;
    color: #333;
    margin-bottom: 4px;
    margin-top: 0;
}

/* Ensure h4 elements in timeline maintain styling */
.timeline-item h4.timeline-project {
    font-weight: 500;
    color: #333;
    margin-bottom: 6px;
    font-size: 0.9rem;
    line-height: 1.3;
    margin-top: 0;
}

/* Ensure h3 elements maintain styling */
h3.contact-title {
    color: #00bfa5;
    font-weight: 600;
    margin-bottom: 16px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0;
}

/* Ensure address element maintains styling */
address.contact-details {
    display: flex;
    gap: 35px;
    font-style: normal;
}

/* Ensure time elements maintain styling */
time.timeline-year {
    font-weight: 500;
    color: #2c5f7a;
    font-size: 0.85rem;
    margin-bottom: 2px;
    display: block;
}

/* Ensure anchor elements in contact section maintain styling */
.contact-section a.contact-email,
.contact-section a.contact-phone {
    color: #00bfa5;
    text-decoration: none;
    font-size: 0.85rem;
    margin-bottom: 2px;
    display: block;
}

.contact-section a.contact-phone {
    color: #666;
}
